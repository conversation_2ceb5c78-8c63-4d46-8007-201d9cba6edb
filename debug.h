/**
 * @file    debug.h
 * @brief   ??????????? - ??????????????
 * <AUTHOR> @date    2025-01-26
 */

#ifndef __DEBUG_H
#define __DEBUG_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdint.h>
#include "ch32v30x.h"

#define GPIOA ((GPIO_TypeDef*)0x40010800)
#define GPIOC ((GPIO_TypeDef*)0x40011000)

#define GPIO_Pin_1  (1 << 1)
#define GPIO_Pin_11 (1 << 11)
#define GPIO_Pin_13 (1 << 13)

#define RCC_APB2Periph_GPIOA 0x00000004
#define RCC_APB2Periph_GPIOC 0x00000010

// ???GPIO????????
#define GPIO_SetBits(port, pin)   do { printf("GPIO_SetBits called\n"); } while(0)
#define GPIO_ResetBits(port, pin) do { printf("GPIO_ResetBits called\n"); } while(0)

// ??????????????
void Delay_Init(void);
void Delay_Us(uint32_t n);
void Delay_Ms(uint32_t n);
void USART_Printf_Init(uint32_t baudrate);

#ifdef __cplusplus
}
#endif

#endif /* __DEBUG_H */
