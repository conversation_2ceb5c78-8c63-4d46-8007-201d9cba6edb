# 智能冲泡控制系统

## 系统概述

本系统是基于CH32V30x微控制器的智能门锁冲泡控制系统，实现了完整的8步自动化冲泡流程控制。

## 主要功能

### 自动化冲泡流程
1. **加水阶段** - 启动水泵1加水至目标水位
2. **等待加热确认** - 等待按键2确认开始加热
3. **加热阶段** - 启动加热器加热水温
4. **等待搅拌确认** - 等待按键3确认开始搅拌
5. **搅拌阶段** - 启动搅拌器进行搅拌
6. **等待停止搅拌** - 等待按键4停止搅拌并监控温度上升1-2°C
7. **静置阶段** - 静置30秒让温度稳定
8. **过滤阶段** - 启动水泵2进行过滤
9. **完成** - 流程完成，显示结果

### 硬件控制
- **水泵控制**：双水泵系统(加水泵/过滤泵)
- **温度监控**：DS18B20温度传感器实时监控
- **加热控制**：智能加热器控制
- **搅拌控制**：360度搅拌器控制
- **水位检测**：自动水位检测
- **按键输入**：4键矩阵键盘
- **显示输出**：LCD液晶显示屏

## 文件结构

### 核心文件
- `brewing_control.c/h` - 冲泡控制核心逻辑
- `main.c` - 主程序入口
- `ds18b20.c/h` - 温度传感器驱动

### 硬件驱动
- `water_pump.c/h` - 水泵控制驱动
- `heater.c/h` - 加热器控制驱动
- `key.c/h` - 按键扫描驱动
- `lcd.c/h` - LCD显示驱动
- `timer.c/h` - 定时器驱动

### 系统文件
- `debug.h` - 调试和GPIO定义
- `ch32v30x.h` - 芯片相关定义

## 使用方法

### 1. 系统启动
- 上电后系统自动初始化所有硬件
- LCD显示系统状态
- 进入空闲状态等待操作

### 2. 开始冲泡
- **按键1**：确认加水并开始完整冲泡流程
- 系统将自动执行8步冲泡流程
- 每个步骤都有LCD提示和状态显示

### 3. 手动控制
- **按键2**：在等待加热阶段确认开始加热
- **按键3**：在等待搅拌阶段开始搅拌
- **按键4**：在搅拌阶段停止搅拌

### 4. 状态监控
- 实时温度显示
- 当前状态指示
- 错误状态提醒
- 超时保护

## 编译说明

```bash
# 编译brewing_control模块
gcc -c brewing_control.c -I. -std=c99

# 编译主程序
gcc -c main.c -I. -std=c99

# 注意：完整编译需要CH32V30x工具链
```

## 技术特性

- **状态机设计**：11个状态的完整状态机
- **任务调度**：100ms周期的任务调度器
- **错误处理**：完善的超时和错误处理机制
- **温度控制**：精确的温度监控和控制
- **硬件抽象**：良好的硬件抽象层设计
- **中文支持**：完整的中文注释和显示

## 安全特性

- **超时保护**：每个阶段都有60秒超时保护
- **温度监控**：实时温度监控防止过热
- **错误恢复**：自动错误检测和恢复
- **状态检查**：完整的系统健康检查

## 调试功能

- **调试模式**：可开启详细的调试输出
- **状态跟踪**：实时状态转换日志
- **性能监控**：系统性能和健康监控
- **错误诊断**：详细的错误诊断信息

## 注意事项

1. 确保所有硬件连接正确
2. 温度传感器必须正确初始化
3. 水位传感器需要定期校准
4. 定期检查水泵和加热器状态
5. 注意系统的超时设置

## 维护说明

- 定期清洁温度传感器
- 检查水泵工作状态
- 校准水位检测精度
- 更新系统参数配置
- 备份重要配置数据
