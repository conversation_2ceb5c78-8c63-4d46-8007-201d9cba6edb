/**
 * @file    brewing_control.h
 * @brief   ????????????????????????????? - 8??????????????
 * <AUTHOR> @date    2025-01-26
 */

#ifndef __BREWING_CONTROL_H
#define __BREWING_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

// === ?????????? ===
#include <stdio.h>
#include <stdint.h>

// === ??????? ===
typedef struct DS18B20_HandleTypeDef DS18B20_HandleTypeDef;

// === ???ó??????? ===
#define BREWING_TASK_PERIOD_MS          100     // ???????????(ms)
#define BREWING_TIMEOUT_SEC             60      // ??????(??)
#define BREWING_REST_TIME_SEC           30      // ???????(??)
#define BREWING_TEMP_THRESHOLD_MIN      1.0f    // ????????????С?(??C)
#define BREWING_TEMP_THRESHOLD_MAX      2.0f    // ??????????????(??C)

// === ????????? ===
#define KEY_WATER_CONFIRM               1       // ????1 - ?????
#define KEY_HEAT_CONFIRM                2       // ????2 - ??????
#define KEY_STIR_START                  3       // ????3 - ???????
#define KEY_STIR_STOP                   4       // ????4 - ??????

// === ?????????? ===
#define STIR_DIRECTION_FORWARD          1       // ???跽?????
#define STIR_SPEED_MAX                  100     // ???????????

// === ???????? ===
typedef enum {
    BREWING_STATE_IDLE = 0,                     // ??????
    BREWING_STATE_FILLING,                      // ?????
    BREWING_STATE_WAIT_HEAT,                    // ????????????
    BREWING_STATE_HEATING,                      // ??????
    BREWING_STATE_WAIT_STIR,                    // ?????????
    BREWING_STATE_STIRRING,                     // ??????
    BREWING_STATE_WAIT_STOP_STIR,               // ???????????
    BREWING_STATE_RESTING,                      // ??????
    BREWING_STATE_FILTERING,                    // ??????
    BREWING_STATE_COMPLETE,                     // ?????
    BREWING_STATE_ERROR                         // ??????
} BrewingState_t;

// === ?????????? ===
typedef enum {
    BREWING_ERROR_NONE = 0,                     // ?????
    BREWING_ERROR_TIMEOUT,                      // ???????
    BREWING_ERROR_TEMPERATURE,                  // ????????????
    BREWING_ERROR_WATER_LEVEL,                  // ?λ??????
    BREWING_ERROR_HARDWARE                      // ???????
} BrewingError_t;

// === ??????嶨?? ===
typedef struct {
    BrewingState_t current_state;               // ?????
    BrewingError_t error_code;                  // ???????
    uint32_t state_start_time;                  // ?????????
    uint32_t water_fill_time;                   // ??????(ms)
    float initial_temperature;                  // ??????
    uint8_t process_active;                     // ?????????
    uint8_t debug_mode;                         // ?????????
} BrewingControl_t;

// === ???????????? ===

/**
 * @brief ????????????
 */
void brewing_control_init(void);

/**
 * @brief ??????????(???????????????)
 */
void brewing_control_proc(void);

/**
 * @brief ????????
 * @return ?????
 */
BrewingState_t brewing_get_current_state(void);

/**
 * @brief ??????????
 * @return ???????
 */
BrewingError_t brewing_get_error_code(void);

/**
 * @brief ??????????????
 */
void brewing_reset_process(void);

/**
 * @brief ???????????豸
 */
void brewing_emergency_stop(void);

/**
 * @brief ?????????
 * @param enable 1-???????, 0-???????
 */
void brewing_set_debug_mode(uint8_t enable);

/**
 * @brief ???????????????
 * @return ?????????(ms)
 */
uint32_t brewing_get_state_duration(void);

/**
 * @brief ???????????
 * @return 1-????, 0-δ????
 */
uint8_t brewing_is_process_active(void);

/**
 * @brief ????????????
 */
void brewing_perform_health_check(void);

#ifdef __cplusplus
}
#endif

#endif /* __BREWING_CONTROL_H */
