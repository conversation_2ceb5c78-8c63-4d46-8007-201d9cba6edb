/**
 * @file    brewing_control.h
 * @brief   智能门锁自动化流程控制模块 - 8步自动化流程管理
 * <AUTHOR> @date    2025-01-26
 */

#ifndef __BREWING_CONTROL_H
#define __BREWING_CONTROL_H

#include "debug.h"
#include "water_pump.h"    // 水泵控制接口
#include "heater.h"        // 加热器控制接口  
#include "ds18b20.h"       // 温度传感器接口
#include "water_level.h"   // 水位检测接口
#include "timer.h"         // 搅拌控制接口
#include "key.h"           // 按键扫描接口

// === 配置常量定义 ===
#define BREWING_TEMP_THRESHOLD_MIN    1.0f    // 温度上升最小阈值(°C)
#define BREWING_TEMP_THRESHOLD_MAX    2.0f    // 温度上升最大阈值(°C)
#define BREWING_REST_TIME_SEC         30      // 静置时间(秒)
#define BREWING_TASK_PERIOD_MS        100     // 任务调度周期(毫秒)
#define BREWING_TIMEOUT_SEC           300     // 操作超时时间(秒)

// === 按键映射定义 ===
#define KEY_WATER_CONFIRM             1       // 按键1-同意加水
#define KEY_HEAT_CONFIRM              2       // 按键2-同意加热
#define KEY_STIR_START                3       // 按键3-开始搅拌
#define KEY_STIR_STOP                 4       // 按键4-停止搅拌

// === 搅拌控制参数 ===
#define STIR_DIRECTION_FORWARD        1       // 搅拌正转方向
#define STIR_SPEED_MAX                255     // 搅拌最大速度

// === 流程状态枚举 ===
typedef enum {
    BREWING_STATE_IDLE = 0,           // 空闲状态-等待按键1
    BREWING_STATE_FILLING,            // 加水中-水泵1工作
    BREWING_STATE_WAIT_HEAT,          // 等待加热确认-等待按键2
    BREWING_STATE_HEATING,            // 加热中-等待按键3
    BREWING_STATE_WAIT_STIR,          // 等待搅拌开始-等待按键3
    BREWING_STATE_STIRRING,           // 搅拌中-等待按键4
    BREWING_STATE_WAIT_STOP_STIR,     // 等待停止搅拌-监控温度
    BREWING_STATE_RESTING,            // 静置中-等待30秒
    BREWING_STATE_FILTERING,          // 过滤中-水泵2工作
    BREWING_STATE_COMPLETE,           // 流程完成
    BREWING_STATE_ERROR               // 错误状态
} BrewingState_t;

// === 错误代码枚举 ===
typedef enum {
    BREWING_ERROR_NONE = 0,           // 无错误
    BREWING_ERROR_WATER_LEVEL,        // 水位检测错误
    BREWING_ERROR_TEMPERATURE,        // 温度传感器错误
    BREWING_ERROR_TIMEOUT,            // 操作超时
    BREWING_ERROR_HARDWARE            // 硬件故障
} BrewingError_t;

// === 流程控制结构体 ===
typedef struct {
    BrewingState_t current_state;     // 当前状态
    BrewingError_t error_code;        // 错误代码
    uint32_t state_start_time;        // 状态开始时间戳(ms)
    uint32_t water_fill_time;         // 加水持续时间(ms)
    float initial_temperature;        // 加热开始时的初始温度
    uint8_t process_active;           // 流程激活标志(1:激活,0:停止)
    uint8_t debug_mode;               // 调试模式标志(1:开启,0:关闭)
} BrewingControl_t;

// === 全局变量声明 ===
extern BrewingControl_t g_brewing_ctrl;
extern DS18B20_HandleTypeDef g_brewing_temp_sensor;

// === 公共函数声明 ===
void brewing_control_init(void);                    // 初始化流程控制模块
void brewing_control_proc(void);                    // 主处理函数(任务调度器调用)
BrewingState_t brewing_get_current_state(void);     // 获取当前状态
BrewingError_t brewing_get_error_code(void);        // 获取错误代码
void brewing_reset_process(void);                   // 重置流程到初始状态
void brewing_emergency_stop(void);                  // 紧急停止所有设备
void brewing_set_debug_mode(uint8_t enable);        // 设置调试模式

// === 内部函数声明(供模块内部使用) ===
static void brewing_state_idle_handler(void);       // 空闲状态处理
static void brewing_state_filling_handler(void);    // 加水状态处理
static void brewing_state_wait_heat_handler(void);  // 等待加热状态处理
static void brewing_state_heating_handler(void);    // 加热状态处理
static void brewing_state_wait_stir_handler(void);  // 等待搅拌状态处理
static void brewing_state_stirring_handler(void);   // 搅拌状态处理
static void brewing_state_wait_stop_stir_handler(void); // 等待停止搅拌状态处理
static void brewing_state_resting_handler(void);    // 静置状态处理
static void brewing_state_filtering_handler(void);  // 过滤状态处理
static void brewing_state_complete_handler(void);   // 完成状态处理
static void brewing_state_error_handler(void);      // 错误状态处理
static void brewing_change_state(BrewingState_t new_state); // 状态切换函数
static uint8_t brewing_check_timeout(uint32_t timeout_ms);  // 超时检查
static void brewing_debug_print_state(void);        // 调试状态输出

#endif /* __BREWING_CONTROL_H */
