/**
 * @file    ch32v30x.h
 * @brief   CH32V30x头文件 - 简化版本用于测试
 * <AUTHOR> @date    2025-01-26
 */

#ifndef __CH32V30X_H
#define __CH32V30X_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

// 基本类型定义
typedef uint32_t u32;
typedef uint16_t u16;
typedef uint8_t  u8;

// GPIO相关定义
typedef struct {
    uint32_t dummy;
} GPIO_TypeDef;

#define GPIOA ((GPIO_TypeDef*)0x40010800)
#define GPIOC ((GPIO_TypeDef*)0x40011000)

#define GPIO_Pin_1  (1 << 1)
#define GPIO_Pin_11 (1 << 11)
#define GPIO_Pin_13 (1 << 13)

#define RCC_APB2Periph_GPIOA 0x00000004
#define RCC_APB2Periph_GPIOC 0x00000010

// GPIO操作函数声明
void GPIO_SetBits(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin);
void GPIO_ResetBits(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin);

#ifdef __cplusplus
}
#endif

#endif /* __CH32V30X_H */
