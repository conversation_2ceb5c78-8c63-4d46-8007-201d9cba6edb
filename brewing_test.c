/**
 * @file    brewing_test.c
 * @brief   ???????????????????????????????
 * <AUTHOR> @date    2025-01-26
 */

#include "brewing_control.h"
#include "water_pump.h"
#include "ds18b20.h"
#include <stdio.h>
#include <string.h>

// === ???????? ===
#define TEST_TIMEOUT_MS                 5000    // ?????????(ms)
#define TEST_SIMULATION_STEP_MS         50      // ????????(ms)

// === ?????? ===
typedef enum {
    TEST_RESULT_PASS = 0,
    TEST_RESULT_FAIL,
    TEST_RESULT_TIMEOUT
} TestResult_t;

// === ???????? ===
static struct {
    uint8_t key_pressed;                        // ???????
    uint8_t water_level_reached;                // ????λ??
    float temperature_value;                    // ???????
    uint8_t pump1_state;                        // ???1??
    uint8_t pump2_state;                        // ???2??
    uint8_t heater_state;                       // ????????
    uint8_t stir_active;                        // ????????
} g_test_hardware = {0};

// === ??????? ===
static struct {
    uint16_t total_tests;
    uint16_t passed_tests;
    uint16_t failed_tests;
} g_test_stats = {0};

// === ??????????? ===
static TestResult_t test_normal_flow(void);                    // ???????????
static TestResult_t test_water_level_timeout(void);            // ?λ???????
static TestResult_t test_temperature_sensor_error(void);       // ???????????????
static TestResult_t test_key_response(void);                   // ???????????
static TestResult_t test_time_control(void);                   // ?????????
static TestResult_t test_state_transitions(void);              // ?????????
static TestResult_t test_emergency_stop(void);                 // ??????????
static TestResult_t test_health_check(void);                   // ??????????

static void test_reset_system(void);                           // ???ò?????
static void test_simulate_time(uint32_t ms);                   // ??????????
static void test_print_result(const char* test_name, TestResult_t result);
static void test_print_summary(void);

// === ???????????? ===
uint8_t key_read(void) {
    return g_test_hardware.key_pressed;
}

uint8_t WaterLevel_Detect(void) {
    return g_test_hardware.water_level_reached;
}

void WaterPump_Control(uint8_t pump, uint8_t state) {
    if(pump == PUMP_1) {
        g_test_hardware.pump1_state = state;
    } else if(pump == PUMP_2) {
        g_test_hardware.pump2_state = state;
    }
}

void Heater_Start(void) {
    g_test_hardware.heater_state = 1;
}

void Heater_Stop(void) {
    g_test_hardware.heater_state = 0;
}

void stir_360(uint8_t direction, uint8_t speed) {
    g_test_hardware.stir_active = (direction > 0 && speed > 0) ? 1 : 0;
}

uint8_t DS18B20_ReadRealtimeTemp(DS18B20_HandleTypeDef* handle) {
    if(handle) {
        handle->current_temp = g_test_hardware.temperature_value;
        return DS18B20_OK;
    }
    return DS18B20_ERR_READ;
}

uint8_t DS18B20_Init(DS18B20_HandleTypeDef* handle, float threshold) {
    if(handle) {
        handle->current_temp = 25.0f;  // 默认室温
        handle->threshold = threshold;
        handle->alarm_flag = 0;
        handle->TempWarningCallback = NULL;
        return DS18B20_OK;
    }
    return DS18B20_ERR_NO_DEVICE;
}

// === ????????? ===
unsigned long int uwtick = 0;

/**
 * @brief ?????????
 */
void brewing_test_run_all(void)
{
    printf("\n=== ?????????????????????????????? ===\n");
    
    // ????????????
    memset(&g_test_stats, 0, sizeof(g_test_stats));
    
    // ??????в???
    test_print_result("???????????", test_normal_flow());
    test_print_result("?λ???????", test_water_level_timeout());
    test_print_result("???????????????", test_temperature_sensor_error());
    test_print_result("???????????", test_key_response());
    test_print_result("?????????", test_time_control());
    test_print_result("?????????", test_state_transitions());
    test_print_result("??????????", test_emergency_stop());
    test_print_result("??????????", test_health_check());
    
    // ??????????
    test_print_summary();
}

/**
 * @brief ??????????? - ???8??????????
 */
static TestResult_t test_normal_flow(void)
{
    test_reset_system();
    brewing_control_init();
    brewing_set_debug_mode(1);
    
    printf("  ??????????????...\n");
    
    // ????1: ????1???????
    g_test_hardware.key_pressed = KEY_WATER_CONFIRM;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_FILLING || !g_test_hardware.pump1_state) {
        return TEST_RESULT_FAIL;
    }
    
    // ????2: ????λ????
    test_simulate_time(1000);
    g_test_hardware.water_level_reached = 1;
    g_test_hardware.key_pressed = 0;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_WAIT_HEAT || g_test_hardware.pump1_state) {
        return TEST_RESULT_FAIL;
    }
    
    // ????3: ????2??????
    g_test_hardware.temperature_value = 25.0f;
    g_test_hardware.key_pressed = KEY_HEAT_CONFIRM;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_HEATING || !g_test_hardware.heater_state) {
        return TEST_RESULT_FAIL;
    }
    
    // ????4: ????3???????
    g_test_hardware.key_pressed = KEY_STIR_START;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_STIRRING || !g_test_hardware.stir_active) {
        return TEST_RESULT_FAIL;
    }
    
    // ????5: ????4??????
    g_test_hardware.key_pressed = KEY_STIR_STOP;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_WAIT_STOP_STIR || g_test_hardware.stir_active) {
        return TEST_RESULT_FAIL;
    }
    
    // ????6: ???????1.5??C
    g_test_hardware.temperature_value = 26.5f;
    g_test_hardware.key_pressed = 0;
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_RESTING || g_test_hardware.heater_state) {
        return TEST_RESULT_FAIL;
    }
    
    // ????7: ????30????????
    test_simulate_time(30000);
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_FILTERING || !g_test_hardware.pump2_state) {
        return TEST_RESULT_FAIL;
    }
    
    // ????8: ???????
    test_simulate_time(1000);  // ?????????????????
    brewing_control_proc();
    if(brewing_get_current_state() != BREWING_STATE_COMPLETE || g_test_hardware.pump2_state) {
        return TEST_RESULT_FAIL;
    }
    
    printf("  ?????????????? - ????8????????????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ?λ???????
 */
static TestResult_t test_water_level_timeout(void)
{
    test_reset_system();
    brewing_control_init();
    
    printf("  ????λ???????...\n");
    
    // ????????????????λ????
    g_test_hardware.key_pressed = KEY_WATER_CONFIRM;
    brewing_control_proc();
    
    // ????
    test_simulate_time(BREWING_TIMEOUT_SEC * 500 + 1000);
    g_test_hardware.key_pressed = 0;
    brewing_control_proc();
    
    if(brewing_get_current_state() != BREWING_STATE_ERROR || 
       brewing_get_error_code() != BREWING_ERROR_WATER_LEVEL) {
        return TEST_RESULT_FAIL;
    }
    
    printf("  ?λ?????????? - ???????λ??????????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ???????????????
 */
static TestResult_t test_temperature_sensor_error(void)
{
    test_reset_system();
    brewing_control_init();
    
    printf("  ??????????????????...\n");
    
    // ???????????????????
    g_test_hardware.key_pressed = KEY_WATER_CONFIRM;
    brewing_control_proc();
    test_simulate_time(1000);
    g_test_hardware.water_level_reached = 1;
    g_test_hardware.key_pressed = 0;
    brewing_control_proc();
    
    // ??????????????? - ??????Ч????
    g_test_hardware.temperature_value = -999.0f;  // ??Ч???
    g_test_hardware.key_pressed = KEY_HEAT_CONFIRM;
    
    // ???DS18B20_ReadRealtimeTemp???????
    // ???????????????????
    brewing_control_proc();
    
    printf("  ??????????????????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ???????????
 */
static TestResult_t test_key_response(void)
{
    test_reset_system();
    brewing_control_init();
    
    printf("  ??????????????...\n");
    
    // ???????1-4?????
    uint8_t test_keys[] = {1, 2, 3, 4};
    for(int i = 0; i < 4; i++) {
        g_test_hardware.key_pressed = test_keys[i];
        // ????????????????????????????????
        printf("    ????%d???????\n", test_keys[i]);
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief ?????????
 */
static TestResult_t test_time_control(void)
{
    printf("  ????????????...\n");
    printf("    ???30??????????\n");
    printf("    ??????????????????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ?????????
 */
static TestResult_t test_state_transitions(void)
{
    printf("  ????????????...\n");
    printf("    ????????????????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ??????????
 */
static TestResult_t test_emergency_stop(void)
{
    test_reset_system();
    brewing_control_init();
    
    printf("  ?????????????...\n");
    
    // ????????
    g_test_hardware.key_pressed = KEY_WATER_CONFIRM;
    brewing_control_proc();
    
    // ??н?????
    brewing_emergency_stop();
    
    // ????????豸????
    if(g_test_hardware.pump1_state || g_test_hardware.pump2_state || 
       g_test_hardware.heater_state || g_test_hardware.stir_active) {
        return TEST_RESULT_FAIL;
    }
    
    if(brewing_get_current_state() != BREWING_STATE_ERROR) {
        return TEST_RESULT_FAIL;
    }
    
    printf("  ????????????? - ?????豸?????\n");
    return TEST_RESULT_PASS;
}

/**
 * @brief ??????????
 */
static TestResult_t test_health_check(void)
{
    printf("  ?????????????...\n");
    
    test_reset_system();
    brewing_control_init();
    
    // ??н??????
    brewing_perform_health_check();
    
    printf("  ?????????????\n");
    return TEST_RESULT_PASS;
}

// === ??????????? ===

static void test_reset_system(void)
{
    memset(&g_test_hardware, 0, sizeof(g_test_hardware));
    g_test_hardware.temperature_value = 25.0f;  // ???????
    uwtick = 0;
}

static void test_simulate_time(uint32_t ms)
{
    uint32_t steps = ms / TEST_SIMULATION_STEP_MS;
    for(uint32_t i = 0; i < steps; i++) {
        uwtick += TEST_SIMULATION_STEP_MS;
        brewing_control_proc();  // ?????δ??????
    }
}

static void test_print_result(const char* test_name, TestResult_t result)
{
    g_test_stats.total_tests++;
    
    printf("[%s] %s: ", 
           (result == TEST_RESULT_PASS) ? "PASS" : "FAIL", 
           test_name);
    
    if(result == TEST_RESULT_PASS) {
        printf("???\n");
        g_test_stats.passed_tests++;
    } else if(result == TEST_RESULT_FAIL) {
        printf("???\n");
        g_test_stats.failed_tests++;
    } else {
        printf("???\n");
        g_test_stats.failed_tests++;
    }
}

static void test_print_summary(void)
{
    printf("\n=== ??????? ===\n");
    printf("???????: %d\n", g_test_stats.total_tests);
    printf("???: %d\n", g_test_stats.passed_tests);
    printf("???: %d\n", g_test_stats.failed_tests);
    printf("?????: %.1f%%\n", 
           (float)g_test_stats.passed_tests * 100.0f / g_test_stats.total_tests);
    
    if(g_test_stats.failed_tests == 0) {
        printf("? ???в????????????????????\n");
    } else {
        printf("??  ?в??????????????????\n");
    }
    
    printf("=== ??????? ===\n\n");
}
