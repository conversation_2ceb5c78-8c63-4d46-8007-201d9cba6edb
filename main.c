/*系统头文件*/
#include "debug.h"            //调试相关头文件
#include "lcd.h"              //液晶显示头文件
#include "pic.h"              //图片头文件
#include "timer.h"            //定时器相关头文件
#include "key.h"              //按键相关头文件
#include "string.h"           //字符串处理头文件
#include "stdio.h"

/*硬件驱动*/
#include "ds18b20.h"          //温度传感器头文件
#include "heater.h"           //加热器控制头文件
#include "water_pump.h"       //水泵控制头文件
#include "water_level.h"      //水位检测头文件
#include "brewing_control.h"  //自动化流程控制头文件


// USART_SendData(USART5, 01);//串口发送数据，参数说明：01-发送数据1，255--发送数据255，可以根据需要修改发送的数据内容
//---这里是串口通信的示例代码，可以用于与其他设备进行数据交换和通信控制



/*函数声明*/
/*中断函数*/
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//定时器3中断处理函数

/*回调函数*/
void TempAlarmHandler(float temp);//温度报警处理函数，当温度超过阈值时触发LED指示灯

/*全局变量*/
unsigned long int uwtick;               //系统时钟计数器，每1毫秒递增1，用于系统时间管理，由TIM3中断更新
u8 key_val,key_down,key_up,key_old;     //按键状态变量
u8 p;                                   //p变量（用于按键状态变化检测）

/*设备句柄*/
//ds18b20
DS18B20_HandleTypeDef ds18b20;          //温度传感器句柄
float threshold = 40.0f; // 设定温度阈值为40度

u8 water_lvel_flag;//水位检测标志位0-未达到目标水位，1-已达到目标水位


/*任务处理函数*/
void key_proc()//按键处理，每10ms调用一次，用于检测按键状态变化
{
    //按键扫描处理
    key_val=key_read();
    p=key_val^key_old;
    key_down=key_val&(key_val^key_old);
    key_up=~key_val&(key_val^key_old);
    key_old=key_val;





}

/*液晶显示处理*/
void lcd_proc()//液晶显示更新处理函数
{

}

void lock_proc()//门锁控制处理,lock_flag=0-解锁，1-上锁
{
    //lock(lock_flag);//门锁控制

}

/*温度处理函数*/
void temp_proc()
{
   uint8_t status;
   status = DS18B20_ReadRealtimeTemp(&ds18b20);
   if(status == DS18B20_OK)
   {
       printf("当前温度：%.2f 度\n", ds18b20.current_temp);
       LCD_ShowFloatNum1(1, 1, ds18b20.current_temp, 2, RED, WHITE, 16);
   }
   else
   {
       printf("温度读取失败\n");
       LCD_ShowString(1, 1, "Temp Error", RED, WHITE, 16, 0);
   }
}

void water_level_proc()
{
    if (WaterLevel_Detect())
   {
       // 检测到水位
       // 可以在这里添加LED指示或其他处理
        water_lvel_flag=1;
   }
   else
   {
       // 未检测到水位
       // 可以在这里添加LED指示或其他处理
       water_lvel_flag=0;
   }
    printf("当前水位检测状态：%d\n", water_lvel_flag);
}



/*定时器3初始化函数，用于系统时钟*///用于更新全局变量uwtick)
void Tim3_Init(u16 arr,u16 psc)//定时器初始化函数，设置定时器周期和分频系数     用于产生定时中断更新系统时钟
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;//定时器基础配置结构体
    NVIC_InitTypeDef NVIC_InitStructure;//中断NVIC配置结构体
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//***使能timer3时钟，必须先使能时钟才能配置定时器

    TIM_TimeBaseInitStruct.TIM_ClockDivision=0;//时钟分频
    TIM_TimeBaseInitStruct.TIM_CounterMode=TIM_CounterMode_Up;//向上计数模式
    TIM_TimeBaseInitStruct.TIM_Period=arr;//自动重装载值，例如5000表示500ms
    TIM_TimeBaseInitStruct.TIM_Prescaler=psc;//设置分频系数

    TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);//***初始化定时器
    TIM_ITConfig(TIM3, TIM_IT_Update|TIM_IT_Trigger, ENABLE);//***使能定时器中断

    NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;//***设置中断通道-定时器中断源
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority=3;
    NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    TIM_Cmd(TIM3, ENABLE);//***启动定时器
}

/*定时器3中断处理函数，每1ms触发一次*/
void TIM3_IRQHandler(void)//中断服务函数
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;    //系统时钟计数器递增



    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/*任务调度器*/
typedef struct{
    void (*task_func)(void);//任务函数指针
    unsigned long int rate_ms;//任务执行周期(ms)
    unsigned long int last_run;//上次执行时间记录
}task_t;
task_t scheduler_task_t[]={
        {lcd_proc,100,0},   //液晶显示任务，每100ms执行一次，0表示初始时间
        {key_proc,10,0},   //按键扫描任务，每10ms执行一次，0表示初始时间
        {lock_proc,30,0},   //门锁控制任务，每30ms执行一次，0表示初始时间
        {brewing_control_proc,100,0}, //自动化流程控制任务，每100ms执行一次
        //{temp_proc,1000,0},//温度检测任务，每1000ms=1s执行一次
        //{water_level_proc,10,0},//水位检测任务
};
unsigned char task_num;//任务数量计数器
void scheduler_init()
{
    task_num=sizeof(scheduler_task_t)/sizeof(task_t);
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;
        if(now_time>=scheduler_task_t[i].rate_ms+scheduler_task_t[i].last_run)
        {
            scheduler_task_t[i].last_run=now_time;
            scheduler_task_t[i].task_func();
        }
    }
}


/*主函数*/
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//中断优先级分组配置
    SystemCoreClockUpdate();//系统时钟更新

    USART_Printf_Init(115200);//串口初始化
    //printf("hello\n");//测试输出
    Delay_Init();//延时函数初始化

//    //加热器测试
//    Heater_Init();   // 初始化加热器模块
//    // 启动加热器测试 1秒钟加热时间
//    Heater_Start();
//    Delay_Ms(10000);  // 延时  1秒钟，注意需要先调用 Delay_Init() 初始化延时
//    Heater_Stop();
//
//    //ds18b20
//    DS18B20_Init(&ds18b20, threshold);  // 初始化DS18B20
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler); // 设置回调函数
    // 初始化DS18B20温度传感器，阈值40度
//    printf("初始化中...\n");
//    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
//        printf("DS18B20初始化失败，请检查硬件连接\n");
//        while(1);
//    }
//    // 设置温度报警回调
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);


//    //搅拌器测试
//    //搅拌器需要先初始化pwm模块
//    TIM2_PWM_Init();//初始化定时器TIM2的PWM功能
//    //lock(0);//解锁门锁测试
//    // 测试搅拌器功能（正转）
//    stir_360(1, 100); // 方向1=正转，速度100=最快
//    Delay_Ms(2000);   // 延时2秒
//    // 测试搅拌器反转
//    stir_360(2, 50);  // 方向2=反转，速度50=中等
//    Delay_Ms(2000);   // 延时2秒
//    // 停止搅拌
//    stir_360(0, 0);   // 停止
//    Delay_Ms(2000);   // 延时2秒
//    // 使用简化版搅拌器 stir函数（开关控制）
//    stir(1);//1=开启(默认正转速度), 0=关闭
//    Delay_Ms(2000);
//    stir(0); // 关闭


    //水泵测试
    // 初始化水泵模块
    WaterPump_Init();
    // 启动水泵1,3秒
    WaterPump_Control(PUMP_1, PUMP_ON_STATE);
    delay_ms(3000);
    // 关闭水泵1,2秒
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    delay_ms(2000);


//    //水位检测M03
//    // 初始化水位检测模块
//    WaterLevel_Init();

    //液晶屏初始化
    LCD_Init();
    LCD_Fill(0,0,127,127,WHITE);//液晶屏全屏填充白色，清除屏幕上可能存在的杂乱显示内容
    //lcd_show_chinese(35,32,"智能门锁",RED,WHITE,16,0);//显示中文标题
    key_init();//初始化按键扫描
    Tim3_Init(1000,96-1);//初始化定时器3中断
    brewing_control_init();//初始化自动化流程控制模块
    scheduler_init();//初始化任务调度器
    while(1)
    {
      scheduler_run();//运行任务调度器
    }
}


// 温度报警处理函数，当温度超过阈值时触发LED指示灯
void TempAlarmHandler(float temp)
{
    printf("警告：温度超过阈值: %.2f°C\n", temp);
    // 示例：GPIO_SetBits(GPIOC, GPIO_Pin_13); // 点亮报警LED
}


