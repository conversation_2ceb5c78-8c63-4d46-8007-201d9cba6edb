/*头文件包含*/
#include "debug.h"            //调试功能头文件
#include "lcd.h"              //液晶显示头文件
#include "pic.h"              //图片资源头文件
#include "timer.h"            //定时器控制头文件
#include "key.h"              //按键检测头文件
#include "string.h"           //字符串处理头文件
#include "stdio.h"

/*硬件驱动*/
#include "heater.h"           //加热器控制头文件
#include "water_pump.h"       //水泵控制头文件
#include "water_level.h"      //水位检测头文件
#include "ds18b20.h"          //温度传感器头文件
#include "brewing_control.h"  //自动化流程控制头文件


// USART_SendData(USART5, 01);//串口发送数据函数，参数01-发送数据1到255--发送数据255，用于控制外部设备或通信
//---此部分代码用于串口通信控制外部设备，根据项目需求进行配置



/*函数声明*/
/*中断函数*/
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//定时器3中断处理函数

/*回调函数*/
void TempAlarmHandler(float temp);//温度报警处理函数，当温度超过阈值时触发LED指示

/*全局变量*/
unsigned long int uwtick;               //系统时钟计数器，每1毫秒递增1，用于任务调度和时间计算，在TIM3中断中更新
u8 key_val,key_down,key_up,key_old;     //按键状态变量
u8 p;                                   //p变量用于检测按键状态变化

/*设备句柄*/
////ds18b20
//DS18B20_HandleTypeDef ds18b20;          //温度传感器句柄
//float threshold = 40.0f; // 设定温度阈值为40度
//
//u8 water_lvel_flag;//水位检测标志位0-未达到水位，1-已达到水位


/*按键处理函数*/
void key_proc()//按键处理函数，10ms调用一次，检测按键状态变化
{
    //按键扫描算法
    key_val=key_read();
    p=key_val^key_old;
    key_down=key_val&(key_val^key_old);
    key_up=~key_val&(key_val^key_old);
    key_old=key_val;





}

/*液晶显示处理函数*/
void lcd_proc()//液晶显示处理函数，定期更新显示内容
{

}

/*温度处理函数*/
void temp_proc()
{
//   uint8_t status;
//   status = DS18B20_ReadRealtimeTemp(&ds18b20);
//   if(status == DS18B20_OK)
//   {
//       printf("当前温度：%.2f 度\n", ds18b20.current_temp);
//       LCD_ShowFloatNum1(1, 1, ds18b20.current_temp, 2, RED, WHITE, 16);
//   }
//   else
//   {
//       printf("温度读取错误\n");
//       LCD_ShowString(1, 1, "Temp Error", RED, WHITE, 16, 0);
//   }
}

void water_level_proc()
{
//    if (WaterLevel_Detect())
//   {
//       // 达到水位
//       // 可以在这里添加LED指示或其他操作
//        water_lvel_flag=1;
//   }
//   else
//   {
//       // 未达到水位
//       // 可以在这里添加LED指示或其他操作
//       water_lvel_flag=0;
//   }
//    printf("当前水位检测状态：%d\n", water_lvel_flag);
}



/*定时器3初始化函数，用于系统时钟计数*///用于更新全局变量uwtick)
void Tim3_Init(u16 arr,u16 psc)//定时器3初始化，配置定时器参数和中断     用于系统时钟和任务调度
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;//定时器基础配置结构体
    NVIC_InitTypeDef NVIC_InitStructure;//中断NVIC配置结构体
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//***使能timer3时钟，必须先使能时钟才能配置定时器

    TIM_TimeBaseInitStruct.TIM_ClockDivision=0;//时钟分频
    TIM_TimeBaseInitStruct.TIM_CounterMode=TIM_CounterMode_Up;//向上计数模式
    TIM_TimeBaseInitStruct.TIM_Period=arr;//自动重装载值，例如5000表示500ms
    TIM_TimeBaseInitStruct.TIM_Prescaler=psc;//设置分频系数

    TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);//***初始化定时器
    TIM_ITConfig(TIM3, TIM_IT_Update|TIM_IT_Trigger, ENABLE);//***使能定时器中断

    NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;//***配置中断通道-定时器中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority=3;
    NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    TIM_Cmd(TIM3, ENABLE);//***启动定时器
}

/*定时器3中断处理函数，每1ms触发一次*/
void TIM3_IRQHandler(void)//中断处理函数
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;    //系统时钟计数器递增



    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/*任务调度器*/
typedef struct{
    void (*task_func)(void);//任务函数指针
    unsigned long int rate_ms;//任务执行周期(ms)
    unsigned long int last_run;//上次执行时间
}task_t;
task_t scheduler_task_t[]={
        {lcd_proc,100,0},   //液晶显示任务，100ms执行一次，0表示初始时间
        {key_proc,10,0},   //按键处理任务，10ms执行一次，0表示初始时间
        {brewing_control_proc,100,0}, //冲泡控制流程任务，100ms执行一次
        //{temp_proc,1000,0},//温度处理任务，1000ms=1s执行一次
        //{water_level_proc,10,0},//水位检测任务
};
unsigned char task_num;//任务数量统计变量
void scheduler_init()
{
    task_num=sizeof(scheduler_task_t)/sizeof(task_t);
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;
        if(now_time>=scheduler_task_t[i].rate_ms+scheduler_task_t[i].last_run)
        {
            scheduler_task_t[i].last_run=now_time;
            scheduler_task_t[i].task_func();
        }
    }
}


/*主函数*/
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//中断优先级分组配置
    SystemCoreClockUpdate();//系统时钟更新

    USART_Printf_Init(115200);//串口初始化
    //printf("hello\n");//串口测试
    Delay_Init();//延时功能初始化

//    //加热器测试代码
//    Heater_Init();   // 初始化加热器模块
//    // 启动加热器测试 1秒钟加热
//    Heater_Start();
//    Delay_Ms(10000);  // 延时  1秒钟，注意这里需要先调用 Delay_Init() 初始化延时
//    Heater_Stop();
//
//    //ds18b20
//    DS18B20_Init(&ds18b20, threshold);  // 初始化DS18B20
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler); // 设置回调函数
    // 初始化DS18B20温度传感器，阈值设为40度
//    printf("初始化温度传感器...\n");
//    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
//        printf("DS18B20初始化失败，请检查硬件连接\n");
//        while(1);
//    }
//    // 设置温度报警回调
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);


//    //搅拌器测试代码
//    //搅拌器需要先初始化pwm功能
//    TIM2_PWM_Init();//搅拌器初始化TIM2的PWM功能
//    //lock(0);//解锁搅拌器控制
//    // 测试搅拌器正转高速运行
//    stir_360(1, 100); // 方向1=正转，速度100=最高
//    Delay_Ms(2000);   // 延时2秒
//    // 测试搅拌器反转中速
//    stir_360(2, 50);  // 方向2=反转，速度50=中等
//    Delay_Ms(2000);   // 延时2秒
//    // 停止搅拌
//    stir_360(0, 0);   // 停止
//    Delay_Ms(2000);   // 延时2秒
//    // 简化搅拌函数 stir函数更简单易用
//    stir(1);//1=启动(默认正转高速), 0=停止
//    Delay_Ms(2000);
//    stir(0); // 停止


    //水泵测试
    // 初始化水泵模块
    WaterPump_Init();
    // 启动水泵1,3秒
    WaterPump_Control(PUMP_1, PUMP_ON_STATE);
    delay_ms(3000);
    // 关闭水泵1,2秒
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    delay_ms(2000);


//    //水位检测M03
//    // 初始化水位检测模块
//    WaterLevel_Init();

    //液晶屏初始化
    LCD_Init();
    LCD_Fill(0,0,127,127,WHITE);//清屏，将整个屏幕填充为白色，坐标从左上角到右下角
    //lcd_show_chinese(35,32,"智能门锁",RED,WHITE,16,0);//显示中文标题
    key_init();//按键初始化函数
    Tim3_Init(1000,96-1);//初始化定时器3中断
    brewing_control_init();//冲泡控制系统初始化
    scheduler_init();//任务调度器初始化
    while(1)
    {
      scheduler_run();//运行任务调度器
    }
}


// 温度报警处理函数，当温度超过阈值时触发LED指示
void TempAlarmHandler(float temp)
{
    printf("警告：温度超过阈值: %.2f°C\n", temp);
    // 示例：GPIO_SetBits(GPIOC, GPIO_Pin_13); // 点亮报警LED
}


