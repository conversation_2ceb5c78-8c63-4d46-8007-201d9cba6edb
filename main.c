/*??????*/
#include "debug.h"            //???????????
#include "lcd.h"              //??????????
#include "pic.h"              //??????
#include "timer.h"            //????????????
#include "key.h"              //???????????
#include "string.h"           //?????????????
#include "stdio.h"

/*硬件驱动*/
#include "heater.h"           //加热器控制头文件
#include "water_pump.h"       //水泵控制头文件
#include "water_level.h"      //水位检测头文件
#include "ds18b20.h"          //温度传感器头文件
#include "brewing_control.h"  //自动化流程控制头文件


// USART_SendData(USART5, 01);//?????????????????????01-????????1??255--????????255???????????????????????????
//---???????????????????????????????????豸???????????????????



/*????????*/
/*?ж????*/
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//?????3?ж????????

/*???????*/
void TempAlarmHandler(float temp);//????????????????????????????????LED????

/*??????*/
unsigned long int uwtick;               //?????????????1???????1??????????????????TIM3?ж????
u8 key_val,key_down,key_up,key_old;     //??????????
u8 p;                                   //p????????????????仯???

/*?豸???*/
////ds18b20
//DS18B20_HandleTypeDef ds18b20;          //???????????
//float threshold = 40.0f; // ?趨???????40??
//
//u8 water_lvel_flag;//?λ?????λ0-δ??????λ??1-???????λ


/*??????????*/
void key_proc()//???????????10ms??????Σ????????????仯
{
    //??????账??
    key_val=key_read();
    p=key_val^key_old;
    key_down=key_val&(key_val^key_old);
    key_up=~key_val&(key_val^key_old);
    key_old=key_val;





}

/*??????????*/
void lcd_proc()//?????????????????
{

}

/*??????????*/
void temp_proc()
{
//   uint8_t status;
//   status = DS18B20_ReadRealtimeTemp(&ds18b20);
//   if(status == DS18B20_OK)
//   {
//       printf("???????%.2f ??\n", ds18b20.current_temp);
//       LCD_ShowFloatNum1(1, 1, ds18b20.current_temp, 2, RED, WHITE, 16);
//   }
//   else
//   {
//       printf("????????\n");
//       LCD_ShowString(1, 1, "Temp Error", RED, WHITE, 16, 0);
//   }
}

void water_level_proc()
{
//    if (WaterLevel_Detect())
//   {
//       // ????λ
//       // ??????????????LED????????????
//        water_lvel_flag=1;
//   }
//   else
//   {
//       // δ????λ
//       // ??????????????LED????????????
//       water_lvel_flag=0;
//   }
//    printf("????λ???????%d\n", water_lvel_flag);
}



/*?????3????????????????????*///?????????????uwtick)
void Tim3_Init(u16 arr,u16 psc)//??????????????????????????????????     ???????????ж?????????
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;//????????????????
    NVIC_InitTypeDef NVIC_InitStructure;//?ж?NVIC???????
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//***???timer3???????????????????????????

    TIM_TimeBaseInitStruct.TIM_ClockDivision=0;//?????
    TIM_TimeBaseInitStruct.TIM_CounterMode=TIM_CounterMode_Up;//?????????
    TIM_TimeBaseInitStruct.TIM_Period=arr;//???????????????5000???500ms
    TIM_TimeBaseInitStruct.TIM_Prescaler=psc;//???÷?????

    TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);//***??????????
    TIM_ITConfig(TIM3, TIM_IT_Update|TIM_IT_Trigger, ENABLE);//***????????ж?

    NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;//***?????ж????-??????ж??
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority=3;
    NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    TIM_Cmd(TIM3, ENABLE);//***?????????
}

/*?????3?ж???????????1ms???????*/
void TIM3_IRQHandler(void)//?ж??????
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;    //??????????????



    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/*?????????*/
typedef struct{
    void (*task_func)(void);//?????????
    unsigned long int rate_ms;//???????????(ms)
    unsigned long int last_run;//???????????
}task_t;
task_t scheduler_task_t[]={
        {lcd_proc,100,0},   //???????????100ms?????Σ?0?????????
        {key_proc,10,0},   //????????????10ms?????Σ?0?????????
        {brewing_control_proc,100,0}, //?????????????????100ms??????
        //{temp_proc,1000,0},//??????????1000ms=1s??????
        //{water_level_proc,10,0},//?λ???????
};
unsigned char task_num;//??????????????
void scheduler_init()
{
    task_num=sizeof(scheduler_task_t)/sizeof(task_t);
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;
        if(now_time>=scheduler_task_t[i].rate_ms+scheduler_task_t[i].last_run)
        {
            scheduler_task_t[i].last_run=now_time;
            scheduler_task_t[i].task_func();
        }
    }
}


/*??????*/
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//?ж??????????????
    SystemCoreClockUpdate();//????????

    USART_Printf_Init(115200);//????????
    //printf("hello\n");//???????
    Delay_Init();//????????????

//    //??????????
//    Heater_Init();   // ??????????????
//    // ?????????????? 1??????????
//    Heater_Start();
//    Delay_Ms(10000);  // ???  1???????????????? Delay_Init() ????????
//    Heater_Stop();
//
//    //ds18b20
//    DS18B20_Init(&ds18b20, threshold);  // ?????DS18B20
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler); // ??????????
    // ?????DS18B20?????????????40??
//    printf("???????...\n");
//    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
//        printf("DS18B20????????????????????\n");
//        while(1);
//    }
//    // ?????????????
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);


//    //??????????
//    //???????????????pwm???
//    TIM2_PWM_Init();//??????????TIM2??PWM????
//    //lock(0);//????????????
//    // ???????????????????
//    stir_360(1, 100); // ????1=????????100=???
//    Delay_Ms(2000);   // ???2??
//    // ????????????
//    stir_360(2, 50);  // ????2=????????50=?е?
//    Delay_Ms(2000);   // ???2??
//    // ??????
//    stir_360(0, 0);   // ??
//    Delay_Ms(2000);   // ???2??
//    // ??ü??????? stir??????????????
//    stir(1);//1=????(?????????), 0=???
//    Delay_Ms(2000);
//    stir(0); // ???


    //??ò???
    // ???????????
    WaterPump_Init();
    // ???????1,3??
    WaterPump_Control(PUMP_1, PUMP_ON_STATE);
    delay_ms(3000);
    // ??????1,2??
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    delay_ms(2000);


//    //?λ???M03
//    // ??????λ??????
//    WaterLevel_Init();

    //??????????
    LCD_Init();
    LCD_Fill(0,0,127,127,WHITE);//?????????????????????????????????????????
    //lcd_show_chinese(35,32,"????????",RED,WHITE,16,0);//??????????
    key_init();//????????????
    Tim3_Init(1000,96-1);//??????????3?ж?
    brewing_control_init();//????????????????????
    scheduler_init();//??????????????
    while(1)
    {
      scheduler_run();//?????????????
    }
}


// ????????????????????????????????LED????
void TempAlarmHandler(float temp)
{
    printf("???棺?????????: %.2f??C\n", temp);
    // ?????GPIO_SetBits(GPIOC, GPIO_Pin_13); // ????????LED
}


