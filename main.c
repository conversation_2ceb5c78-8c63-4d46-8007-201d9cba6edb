/*ͷ�ļ�������*/
#include "debug.h"            //��Ҫϵͳͷ�ļ�
#include "lcd.h"              //��Ļͷ�ļ�
#include "pic.h"              //ͼƬͷ�ļ�
#include "timer.h"            //��ʱ��ͷ�ļ��������
#include "key.h"              //���󰴼�ͷ�ļ�
#include "string.h"           //���鴦����غ���ͷ�ļ�
#include "stdio.h"

/*�¼ӵ�ͷ�ļ�*/
#include "ds18b20.h"          //�¶ȴ�������ͷ�ļ�
#include "heater.h"           //����Ƭ��������ͷ�ļ�
#include "water_pump.h"       //ˮ�ô�������ͷ�ļ�
#include "water_level.h"      //��ˮλ��������ͷ�ļ�
#include "brewing_control.h"  //�Զ�������̿���ģ��ͷ�ļ�


// USART_SendData(USART5, 01);//��򵥵�˼·��ʵ�ʲ����ã�̫Ƿȱ�ˣ���01-���ŵ�1�ף�255--���ŵ�255��,���ǿ�������Ҳ������д�����Բ�����������ʵ�ּ򵥵���������
//---ͨ�����ݰ�����ʽ��һ�η��Ͷ�����ݣ�������һ��ʲôʲô���ݱ���Ҫ���ŵ�һ�����������������������һ���ģ��������治��ȡ��



/*����������*/
/*֮ǰ�ĺ���*/
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//��ʱ��3�����ж�

/*�¼ӵĺ���*/
void TempAlarmHandler(float temp);//�¶ȱ����ص��������û����Զ��壬��������LED�������ȣ�


/*����������*/
unsigned long int uwtick;               //ϵͳ��ʱ������ÿһ���룫1�����ö�ʱ�������Դӵ�һ����TIM3��ֲ���룩
u8 key_val,key_down,key_up,key_old;     //������ر���
u8 p;                                   //p����(�Ȱ����ϱ��أ�Ҳ�����±���)

/*�¼ӵı���*/
//ds18b20
DS18B20_HandleTypeDef ds18b20;          //
float threshold = 40.0f; // �趨������ֵΪ40��

u8 water_lvel_flag;//ˮλ����־λ0-δ��ָ��ˮ��λ�ã�1-�ﵽָ��ˮ��λ��


/*������������*/
void key_proc()//ÿ��10ms��ȡһ�Σ���Ϊ�˷�ֹ10ms�Ļ�е����
{
    //���а�������
    key_val=key_read();
    p=key_val^key_old;
    key_down=key_val&(key_val^key_old);
    key_up=~key_val&(key_val^key_old);
    key_old=key_val;





}

/*��Ļ��������*/
void lcd_proc()//����������������Ļ����ʾ��
{

}

void lock_proc()//������������,lock_flag=0-���ţ�1-����
{
    //lock(lock_flag);//���ض��

}

/*ʵʱ�¶ȶ�ȡ����*/
void temp_proc()
{
   uint8_t status;
   status = DS18B20_ReadRealtimeTemp(&ds18b20);
   if(status == DS18B20_OK)
   {
       printf("��ǰˮ�£�%.2f ��\n", ds18b20.current_temp);
       LCD_ShowFloatNum1(1, 1, ds18b20.current_temp, 2, RED, WHITE, 16);
   }
   else
   {
       printf("�¶ȶ�ȡʧ��\n");
       LCD_ShowString(1, 1, "Temp Error", RED, WHITE, 16, 0);
   }
}

void water_level_proc()
{
    if (WaterLevel_Detect())
   {
       // ��⵽��Һ��
       // ���Ե���LED�������������
        water_lvel_flag=1;
   }
   else
   {
       // δ��⵽Һ��
       // ����Ϩ��LED�������������
       water_lvel_flag=0;
   }
    printf("��ǰ�Ƿ�ﵽָ��ˮλ��%d\n", water_lvel_flag);
}



/*ϵͳ��ʱ��ʱ��3�жϷ�������ÿһ���룩*///����ϵͳ��ʱ������uwtick)
void Tim3_Init(u16 arr,u16 psc)//���Զ���װ�ص�ֵ������ֵ��������������г�ʼ��     �����˶�ʱ���Ŀ����ж�����Ҳ��Ҫ��ֲ����
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;//�������ö�ʱ���Ľṹ��
    NVIC_InitTypeDef NVIC_InitStructure;//����NVIC�Ľṹ��
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//***��timer3��ʱ�ӣ�ע���������һ�������ϣ�

    TIM_TimeBaseInitStruct.TIM_ClockDivision=0;//ʱ�ӷָ�
    TIM_TimeBaseInitStruct.TIM_CounterMode=TIM_CounterMode_Up;//����Ϊ���ϼ���
    TIM_TimeBaseInitStruct.TIM_Period=arr;//�Զ���װ��ֵ��5000Ϊ500ms
    TIM_TimeBaseInitStruct.TIM_Prescaler=psc;//���÷�Ƶֵ

    TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);//***
    TIM_ITConfig(TIM3, TIM_IT_Update|TIM_IT_Trigger, ENABLE);//***

    NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;//***�����ߣ�����-û���ж�ͨ��
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority=3;
    NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    TIM_Cmd(TIM3, ENABLE);//***
}

/*ϵͳ��ʱ��ʱ��3�жϷ�������1ms*/
void TIM3_IRQHandler(void)//�жϷ�����
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;    //ϵͳ��ʱ��������



    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/*���������*/
typedef struct{
    void (*task_func)(void);//������
    unsigned long int rate_ms;//����ִ������
    unsigned long int last_run;//�����ϴ�����ʱ��
}task_t;
task_t scheduler_task_t[]={
        {lcd_proc,100,0},   //��Ļ����������100msִ��һ�Σ�0�뿪ʼִ��
        {key_proc,10,0},   //��������������10msִ��һ�Σ�0�뿪ʼִ��
        {lock_proc,30,0},   //��������������30msִ��һ�Σ�0�뿪ʼִ��
        {brewing_control_proc,100,0}, //�Զ�������̿���������100msִ��һ��
        //{temp_proc,1000,0},//�¶ȴ���������1000ms=1sִ��һ��
        //{water_level_proc,10,0},//ˮλ��⴦������
};
unsigned char task_num;//������������
void scheduler_init()
{
    task_num=sizeof(scheduler_task_t)/sizeof(task_t);
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;
        if(now_time>=scheduler_task_t[i].rate_ms+scheduler_task_t[i].last_run)
        {
            scheduler_task_t[i].last_run=now_time;
            scheduler_task_t[i].task_func();
        }
    }
}


/*������*/
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//�ж����ȼ�����
    SystemCoreClockUpdate();//ʱ������

    USART_Printf_Init(115200);//����һ��ʼ��
    //printf("hello\n");//����
    Delay_Init();//��ʱ������ʼ��

//    //����Ƭ
//    Heater_Init();   // ��ʼ������Ƭ����
//    // ʾ������������Ƭ 1���ӣ�Ȼ��ֹͣ
//    Heater_Start();
//    Delay_Ms(10000);  // ��ʱ  1���ӣ���ȷ�� Delay_Init() �ѳ�ʼ����
//    Heater_Stop();
//
//    //ds18b20
//    DS18B20_Init(&ds18b20, threshold);  // ��ʼ��DS18B20
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler); // ���ñ����ص�
    // ��ʼ��DS18B20��������ֵ��Ϊ40��
//    printf("ϵͳ����...\n");
//    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
//        printf("DS18B20��ʼ��ʧ�ܣ����鴫��������\n");
//        while(1);
//    }
//    // ���¶ȱ����ص�
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);


//    //���
//    //�ö�ʱ�������ƶ��������pwm���
//    TIM2_PWM_Init();//�����ʼ����TIM2���PWM��
//    //lock(0);//���ö��ʹ��
//    // �ö����ת������ٶȽ��裩
//    stir_360(1, 100); // ����1=��ת���ٶ�100=���
//    Delay_Ms(2000);   // ����2��
//    // ��Ϊ��ת�����٣�
//    stir_360(2, 50);  // ����2=��ת���ٶ�50=����
//    Delay_Ms(2000);   // ��ת2��
//    // ֹͣ����
//    stir_360(0, 0);   // ֹͣ
//    Delay_Ms(2000);   // ֹͣ2��
//    // Ҳ�����ü��ݺ��� stir������ΪĬ����ת����
//    stir(1);//1=����(Ĭ��������ת), 0=ֹͣ
//    Delay_Ms(2000);
//    stir(0); // ֹͣ


    //ˮ��
    // ˮ��ϵͳ��ʼ��
    WaterPump_Init();
    // ����ˮ��1,3��
    WaterPump_Control(PUMP_1, PUMP_ON_STATE);
    delay_ms(3000);
    // �ر�ˮ��1,2��
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    delay_ms(2000);


//    //ˮλ���M03
//    // ��ʼ��ˮλ���ģ��
//    WaterLevel_Init();

    //��Ļ
    LCD_Init();
    LCD_Fill(0,0,127,127,WHITE);//�������Ļ���ԭ�������Ĵ�����ʾ�Ļ��ǻ�����ʾ��ֻ������һ�е���Щ�����л��г��ֶ�Ӧ����ɫ
    //lcd_show_chinese(35,32,"�����ɹ�",RED,WHITE,16,0);//�����ɹ�
    key_init();//���ü��̳�ʼ��
    Tim3_Init(1000,96-1);//ϵͳ��ʱ��3��ʼ��
    brewing_control_init();//�Զ�������̿���ģ����ʼ��
    scheduler_init();//��������ʼ��
    while(1)
    {
      scheduler_run();//����������
    }
}


// �¶ȱ����ص��������û����Զ��壬��������LED�������ȣ�
void TempAlarmHandler(float temp)
{
    printf("���棺ˮ�¹��ߣ���ǰ�¶�: %.2f��C\n", temp);
    // ���磺GPIO_SetBits(GPIOC, GPIO_Pin_13); // ����������
}


