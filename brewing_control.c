/**
 * @file    brewing_control.c
 * @brief   智能冲泡控制系统核心实现 - 8步自动化冲泡流程
 * <AUTHOR> @date    2025-01-26
 */

#include "brewing_control.h"
#include "ds18b20.h"  // 包含DS18B20头文件以使用完整类型定义
#include "key.h"      // 按键控制
#include "water_pump.h" // 水泵控制
#include "heater.h"   // 加热器控制
#include "water_level.h" // 水位检测
#include "timer.h"    // 定时器
#include "lcd.h"      // 液晶显示
#include "debug.h"    // 调试输出

// === 外部变量声明 ===
extern unsigned long int uwtick;  // 系统时钟计数(定义在main.c)

// === 全局变量定义 ===
BrewingControl_t g_brewing_ctrl = {0};              // 冲泡控制结构体
DS18B20_HandleTypeDef g_brewing_temp_sensor = {0};  // 温度传感器句柄

// === 静态函数声明 ===
static void brewing_state_idle_handler(void);
static void brewing_state_filling_handler(void);
static void brewing_state_wait_heat_handler(void);
static void brewing_state_heating_handler(void);
static void brewing_state_wait_stir_handler(void);  // 等待搅拌状态处理
static void brewing_state_stirring_handler(void);
static void brewing_state_wait_stop_stir_handler(void);
static void brewing_state_resting_handler(void);
static void brewing_state_filtering_handler(void);
static void brewing_state_complete_handler(void);
static void brewing_state_error_handler(void);
static void brewing_change_state(BrewingState_t new_state);
static uint8_t brewing_check_timeout(uint32_t timeout_ms);
static void brewing_debug_print_state(void);
static void brewing_emergency_stop_all(void);
static void brewing_system_health_check(void);
static void brewing_log_state_transition(BrewingState_t old_state, BrewingState_t new_state);

/**
 * @brief 冲泡控制系统初始化
 */
void brewing_control_init(void)
{
    // 初始化控制结构体
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.state_start_time = uwtick;
    g_brewing_ctrl.water_fill_time = 0;
    g_brewing_ctrl.initial_temperature = 0.0f;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.debug_mode = 0;

    // 初始化温度传感器
    DS18B20_Init(&g_brewing_temp_sensor, 100.0f);  // 设置最大温度

    // 显示初始化完成信息
    brewing_emergency_stop_all();

    if(g_brewing_ctrl.debug_mode) {
        printf("冲泡控制: 初始化完成\n");
    }
}

/**
 * @brief 冲泡控制主处理函数(任务调度器调用)
 */
void brewing_control_proc(void)
{
    // 系统健康检查
    if(g_brewing_ctrl.current_state != BREWING_STATE_IDLE &&
       g_brewing_ctrl.current_state != BREWING_STATE_ERROR) {
        brewing_system_health_check();
    }

    // 流程激活检查
    if(!g_brewing_ctrl.process_active && g_brewing_ctrl.current_state != BREWING_STATE_IDLE) {
        return;
    }

    // 状态处理
    switch(g_brewing_ctrl.current_state) {
        case BREWING_STATE_IDLE:
            brewing_state_idle_handler();
            break;
        case BREWING_STATE_FILLING:
            brewing_state_filling_handler();
            break;
        case BREWING_STATE_WAIT_HEAT:
            brewing_state_wait_heat_handler();
            break;
        case BREWING_STATE_HEATING:
            brewing_state_heating_handler();
            break;
        case BREWING_STATE_WAIT_STIR:
            brewing_state_wait_stir_handler();
            break;
        case BREWING_STATE_STIRRING:
            brewing_state_stirring_handler();
            break;
        case BREWING_STATE_WAIT_STOP_STIR:
            brewing_state_wait_stop_stir_handler();
            break;
        case BREWING_STATE_RESTING:
            brewing_state_resting_handler();
            break;
        case BREWING_STATE_FILTERING:
            brewing_state_filtering_handler();
            break;
        case BREWING_STATE_COMPLETE:
            brewing_state_complete_handler();
            break;
        case BREWING_STATE_ERROR:
            brewing_state_error_handler();
            break;
        default:
            g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
            brewing_change_state(BREWING_STATE_ERROR);
            break;
    }
}

/**
 * @brief 空闲状态处理 - 等待按键1开始冲泡流程
 */
static void brewing_state_idle_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_WATER_CONFIRM) {  // 按键1 - 开始加水
        g_brewing_ctrl.process_active = 1;
        g_brewing_ctrl.water_fill_time = 0;

        // 启动水泵1开始加水
        WaterPump_Control(PUMP_1, PUMP_ON_STATE);

        brewing_change_state(BREWING_STATE_FILLING);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 开始加水\n");
        }
    }
}

/**
 * @brief 加水状态处理 - 监控水位直到达到目标水位
 */
static void brewing_state_filling_handler(void)
{
    // 超时检查
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // 累计加水时间
    g_brewing_ctrl.water_fill_time += BREWING_TASK_PERIOD_MS;

    // 水位检测
    uint8_t water_level_status = WaterLevel_Detect();
    if(water_level_status == 1) {  // 达到目标水位
        WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // 关闭水泵1
        brewing_change_state(BREWING_STATE_WAIT_HEAT);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 水位已达到，加水时间: %d毫秒\n", g_brewing_ctrl.water_fill_time);
        }
    } else {
        // 检查是否加水时间过长但水位未达到
        if(g_brewing_ctrl.water_fill_time > (BREWING_TIMEOUT_SEC * 500)) {  // 超过一半超时时间仍未达到水位
            if(g_brewing_ctrl.debug_mode) {
                printf("冲泡: 水位传感器可能故障\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_WATER_LEVEL;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }
}

/**
 * @brief 等待加热确认状态 - 等待按键2确认开始加热
 */
static void brewing_state_wait_heat_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_HEAT_CONFIRM) {  // 按键2 - 确认加热
        // 读取初始温度
        if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
            g_brewing_ctrl.initial_temperature = g_brewing_temp_sensor.current_temp;

            // 启动加热器
            Heater_Start();
            brewing_change_state(BREWING_STATE_HEATING);
        } else {
            // 温度传感器读取失败
            if(g_brewing_ctrl.debug_mode) {
                printf("冲泡: 开始加热时温度传感器错误\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
            brewing_change_state(BREWING_STATE_ERROR);
            return;
        }

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 开始加热，初始温度: %.1f°C\n", g_brewing_ctrl.initial_temperature);
        }
    }
}

/**
 * @brief 加热状态处理 - 等待按键3确认开始搅拌
 */
static void brewing_state_heating_handler(void)
{
    uint8_t key = key_read();

    // 超时检查
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    if(key == KEY_STIR_START) {  // 按键3 - 开始搅拌
        // 保持加热器开启(继续加热)
        stir_360(STIR_DIRECTION_FORWARD, STIR_SPEED_MAX);
        brewing_change_state(BREWING_STATE_STIRRING);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 开始搅拌\n");
        }
    }
}

/**
 * @brief 等待搅拌确认状态 - 等待按键3确认开始搅拌
 */
static void brewing_state_wait_stir_handler(void)
{
    uint8_t key = key_read();

    // 超时检查
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    if(key == KEY_STIR_START) {  // 按键3 - 开始搅拌
        // 保持加热器开启(继续加热搅拌)
        stir_360(STIR_DIRECTION_FORWARD, STIR_SPEED_MAX);
        brewing_change_state(BREWING_STATE_STIRRING);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 开始搅拌\n");
        }
    }
}

/**
 * @brief 搅拌状态处理 - 等待按键4停止搅拌
 */
static void brewing_state_stirring_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_STIR_STOP) {  // 按键4 - 停止搅拌
        stir_360(0, 0);  // 停止搅拌
        brewing_change_state(BREWING_STATE_WAIT_STOP_STIR);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 停止搅拌，监控温度\n");
        }
    }
}

/**
 * @brief 等待停止搅拌状态 - 监控温度上升1-2°C后进入静置
 */
static void brewing_state_wait_stop_stir_handler(void)
{
    float current_temp;

    // 超时检查
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // 读取当前温度
    if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
        current_temp = g_brewing_temp_sensor.current_temp;
        float temp_rise = current_temp - g_brewing_ctrl.initial_temperature;

        // 检查温度是否上升1-2°C
        if(temp_rise >= BREWING_TEMP_THRESHOLD_MIN && temp_rise <= BREWING_TEMP_THRESHOLD_MAX) {
            Heater_Stop();  // 停止加热
            brewing_change_state(BREWING_STATE_RESTING);

            if(g_brewing_ctrl.debug_mode) {
                printf("冲泡: 温度上升%.1f°C，停止加热\n", temp_rise);
            }
        }
    } else {
        g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
        brewing_change_state(BREWING_STATE_ERROR);
    }
}

/**
 * @brief 静置状态处理 - 静置30秒让温度稳定
 */
static void brewing_state_resting_handler(void)
{
    uint32_t rest_time = (uwtick - g_brewing_ctrl.state_start_time);

    if(rest_time >= (BREWING_REST_TIME_SEC * 1000)) {  // 30秒静置完成
        // 启动水泵2开始过滤
        WaterPump_Control(PUMP_2, PUMP_ON_STATE);
        brewing_change_state(BREWING_STATE_FILTERING);

        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 开始过滤\n");
        }
    }
}

/**
 * @brief 过滤状态处理 - 过滤时间等于加水时间
 */
static void brewing_state_filtering_handler(void)
{
    uint32_t filter_time = (uwtick - g_brewing_ctrl.state_start_time);

    if(filter_time >= g_brewing_ctrl.water_fill_time) {  // 过滤时间等于加水时间
        WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // 关闭水泵2
        brewing_change_state(BREWING_STATE_COMPLETE);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡: 过滤完成，总时间: %d毫秒\n", filter_time);
        }
    }
}

/**
 * @brief 完成状态处理 - 冲泡流程完成
 */
static void brewing_state_complete_handler(void)
{
    g_brewing_ctrl.process_active = 0;  // 停止流程

    if(g_brewing_ctrl.debug_mode) {
        printf("冲泡控制: 流程成功完成\n");
    }
}

/**
 * @brief 错误状态处理 - 处理各种错误情况
 */
static void brewing_state_error_handler(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.process_active = 0;

    if(g_brewing_ctrl.debug_mode) {
        printf("冲泡控制: 错误发生，代码: %d\n", g_brewing_ctrl.error_code);
    }
}

/**
 * @brief 状态切换函数
 */
static void brewing_change_state(BrewingState_t new_state)
{
    BrewingState_t old_state = g_brewing_ctrl.current_state;

    // 记录状态转换
    brewing_log_state_transition(old_state, new_state);

    g_brewing_ctrl.current_state = new_state;
    g_brewing_ctrl.state_start_time = uwtick;

    if(g_brewing_ctrl.debug_mode) {
        brewing_debug_print_state();
    }
}

/**
 * @brief 超时检查
 */
static uint8_t brewing_check_timeout(uint32_t timeout_ms)
{
    return ((uwtick - g_brewing_ctrl.state_start_time) >= timeout_ms);
}

/**
 * @brief 调试状态打印
 */
static void brewing_debug_print_state(void)
{
    const char* state_names[] = {
        "空闲", "加水", "等待加热", "加热", "等待搅拌",
        "搅拌", "等待停止搅拌", "静置", "过滤", "完成", "错误"
    };

    if(g_brewing_ctrl.current_state <= BREWING_STATE_ERROR) {
        printf("冲泡控制: 状态: %s\n", state_names[g_brewing_ctrl.current_state]);
    }
}

/**
 * @brief 紧急停止所有设备
 */
static void brewing_emergency_stop_all(void)
{
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // 关闭水泵1
    WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // 关闭水泵2
    Heater_Stop();                              // 停止加热
    stir_360(0, 0);                            // 停止搅拌
}

// === 外部接口函数实现 ===

/**
 * @brief 获取当前状态
 */
BrewingState_t brewing_get_current_state(void)
{
    return g_brewing_ctrl.current_state;
}

/**
 * @brief 获取错误代码
 */
BrewingError_t brewing_get_error_code(void)
{
    return g_brewing_ctrl.error_code;
}

/**
 * @brief 重置冲泡流程
 */
void brewing_reset_process(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.state_start_time = uwtick;

    if(g_brewing_ctrl.debug_mode) {
        printf("冲泡控制: 流程重置\n");
    }
}

/**
 * @brief 紧急停止冲泡流程
 */
void brewing_emergency_stop(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
    brewing_change_state(BREWING_STATE_ERROR);
}

/**
 * @brief 设置调试模式
 */
void brewing_set_debug_mode(uint8_t enable)
{
    g_brewing_ctrl.debug_mode = enable;
}

/**
 * @brief 系统健康检查
 */
static void brewing_system_health_check(void)
{
    // 检查温度传感器状态
    if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) != DS18B20_OK) {
        if(g_brewing_ctrl.debug_mode) {
            printf("冲泡控制: 健康检查: 温度传感器错误\n");
        }
        g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // 检查水位传感器状态
    uint8_t water_level = WaterLevel_Detect();
    if(g_brewing_ctrl.current_state == BREWING_STATE_FILLING && water_level == 0) {
        // 如果在加水状态但长时间未检测到水位变化
        if(g_brewing_ctrl.water_fill_time > (BREWING_TIMEOUT_SEC * 800)) {
            if(g_brewing_ctrl.debug_mode) {
                printf("冲泡控制: 健康检查: 水位传感器超时\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_WATER_LEVEL;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }

    // 检查状态超时
    if(g_brewing_ctrl.current_state != BREWING_STATE_IDLE &&
       g_brewing_ctrl.current_state != BREWING_STATE_COMPLETE &&
       g_brewing_ctrl.current_state != BREWING_STATE_ERROR) {
        uint32_t state_duration = uwtick - g_brewing_ctrl.state_start_time;
        if(state_duration > (BREWING_TIMEOUT_SEC * 1200)) {  // 超过最大超时
            if(g_brewing_ctrl.debug_mode) {
                printf("冲泡控制: 健康检查: 状态超时, 持续时间: %dms\n", state_duration);
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }
}

/**
 * @brief 记录状态转换
 */
static void brewing_log_state_transition(BrewingState_t old_state, BrewingState_t new_state)
{
    const char* state_names[] = {
        "空闲", "加水", "等待加热", "加热", "等待搅拌",
        "搅拌", "等待停止搅拌", "静置", "过滤", "完成", "错误"
    };

    if(g_brewing_ctrl.debug_mode && old_state != new_state) {
        if(old_state <= BREWING_STATE_ERROR && new_state <= BREWING_STATE_ERROR) {
            printf("冲泡控制: 状态转换: %s -> %s (时间: %d毫秒)\n",
                   state_names[old_state], state_names[new_state], uwtick);
        }
    }
}

/**
 * @brief 获取当前状态持续时间
 */
uint32_t brewing_get_state_duration(void)
{
    return (uwtick - g_brewing_ctrl.state_start_time);
}

/**
 * @brief 检查冲泡流程是否激活
 */
uint8_t brewing_is_process_active(void)
{
    return g_brewing_ctrl.process_active;
}

/**
 * @brief 执行健康检查
 */
void brewing_perform_health_check(void)
{
    brewing_system_health_check();
}
