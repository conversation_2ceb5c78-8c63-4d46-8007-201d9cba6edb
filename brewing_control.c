/**
 * @file    brewing_control.c
 * @brief   ���������Զ������̿���ģ��ʵ�� - 8���Զ������̹���
 * <AUTHOR> @date    2025-01-26
 */

#include "brewing_control.h"

// === �ⲿ�������� ===
extern unsigned long int uwtick;  // ϵͳʱ�Ӽ�����(����main.c)

// === ȫ�ֱ������� ===
BrewingControl_t g_brewing_ctrl = {0};              // ���̿��ƽṹ��
DS18B20_HandleTypeDef g_brewing_temp_sensor = {0};  // �¶ȴ��������

// === ˽�к������� ===
static void brewing_state_idle_handler(void);
static void brewing_state_filling_handler(void);
static void brewing_state_wait_heat_handler(void);
static void brewing_state_heating_handler(void);
static void brewing_state_wait_stir_handler(void);
static void brewing_state_stirring_handler(void);
static void brewing_state_wait_stop_stir_handler(void);
static void brewing_state_resting_handler(void);
static void brewing_state_filtering_handler(void);
static void brewing_state_complete_handler(void);
static void brewing_state_error_handler(void);
static void brewing_change_state(BrewingState_t new_state);
static uint8_t brewing_check_timeout(uint32_t timeout_ms);
static void brewing_debug_print_state(void);
static void brewing_emergency_stop_all(void);

/**
 * @brief ��ʼ�����̿���ģ��
 */
void brewing_control_init(void)
{
    // ��ʼ�����ƽṹ��
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.state_start_time = uwtick;
    g_brewing_ctrl.water_fill_time = 0;
    g_brewing_ctrl.initial_temperature = 0.0f;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.debug_mode = 0;
    
    // ��ʼ���¶ȴ�����
    DS18B20_Init(&g_brewing_temp_sensor, 100.0f);  // ���ø��¶���ֵ
    
    // ȷ�������豸���ڰ�ȫ״̬
    brewing_emergency_stop_all();
    
    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing Control: Initialized\n");
    }
}

/**
 * @brief ����������(�������������)
 */
void brewing_control_proc(void)
{
    // ��������Ƿ񼤻�
    if(!g_brewing_ctrl.process_active && g_brewing_ctrl.current_state != BREWING_STATE_IDLE) {
        return;
    }
    
    // ״̬������
    switch(g_brewing_ctrl.current_state) {
        case BREWING_STATE_IDLE:
            brewing_state_idle_handler();
            break;
        case BREWING_STATE_FILLING:
            brewing_state_filling_handler();
            break;
        case BREWING_STATE_WAIT_HEAT:
            brewing_state_wait_heat_handler();
            break;
        case BREWING_STATE_HEATING:
            brewing_state_heating_handler();
            break;
        case BREWING_STATE_WAIT_STIR:
            brewing_state_wait_stir_handler();
            break;
        case BREWING_STATE_STIRRING:
            brewing_state_stirring_handler();
            break;
        case BREWING_STATE_WAIT_STOP_STIR:
            brewing_state_wait_stop_stir_handler();
            break;
        case BREWING_STATE_RESTING:
            brewing_state_resting_handler();
            break;
        case BREWING_STATE_FILTERING:
            brewing_state_filtering_handler();
            break;
        case BREWING_STATE_COMPLETE:
            brewing_state_complete_handler();
            break;
        case BREWING_STATE_ERROR:
            brewing_state_error_handler();
            break;
        default:
            g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
            brewing_change_state(BREWING_STATE_ERROR);
            break;
    }
}

/**
 * @brief ����״̬���� - �ȴ�����1��������
 */
static void brewing_state_idle_handler(void)
{
    uint8_t key = key_read();
    
    if(key == KEY_WATER_CONFIRM) {  // ����1 - ͬ���ˮ
        g_brewing_ctrl.process_active = 1;
        g_brewing_ctrl.water_fill_time = 0;
        
        // ����ˮ��1��ʼ��ˮ
        WaterPump_Control(PUMP_1, PUMP_ON_STATE);
        
        brewing_change_state(BREWING_STATE_FILLING);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start filling water\n");
        }
    }
}

/**
 * @brief ��ˮ״̬���� - ���ˮλ�������ֹͣˮ��
 */
static void brewing_state_filling_handler(void)
{
    // ��鳬ʱ
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }
    
    // �ۼƼ�ˮʱ��
    g_brewing_ctrl.water_fill_time += BREWING_TASK_PERIOD_MS;
    
    // ���ˮλ
    if(WaterLevel_Detect() == 1) {  // ����ָ��ˮλ
        WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // ֹͣˮ��1
        brewing_change_state(BREWING_STATE_WAIT_HEAT);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Water level reached, fill time: %dms\n", g_brewing_ctrl.water_fill_time);
        }
    }
}

/**
 * @brief �ȴ�����ȷ��״̬ - �ȴ�����2
 */
static void brewing_state_wait_heat_handler(void)
{
    uint8_t key = key_read();
    
    if(key == KEY_HEAT_CONFIRM) {  // ����2 - ͬ�����
        // ��¼��ʼ�¶�
        if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
            g_brewing_ctrl.initial_temperature = g_brewing_temp_sensor.current_temp;
        }
        
        // ��������
        Heater_Start();
        brewing_change_state(BREWING_STATE_HEATING);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start heating, initial temp: %.1f��C\n", g_brewing_ctrl.initial_temperature);
        }
    }
}

/**
 * @brief ����״̬���� - �ȴ�����3��ʼ����
 */
static void brewing_state_heating_handler(void)
{
    uint8_t key = key_read();
    
    // ��鳬ʱ
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }
    
    if(key == KEY_STIR_START) {  // ����3 - ��ʼ����
        // ��������(��ת����ٶ�)
        stir_360(STIR_DIRECTION_FORWARD, STIR_SPEED_MAX);
        brewing_change_state(BREWING_STATE_STIRRING);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start stirring\n");
        }
    }
}

/**
 * @brief ����״̬���� - �ȴ�����4ֹͣ����
 */
static void brewing_state_stirring_handler(void)
{
    uint8_t key = key_read();
    
    if(key == KEY_STIR_STOP) {  // ����4 - ֹͣ����
        stir_360(0, 0);  // ֹͣ����
        brewing_change_state(BREWING_STATE_WAIT_STOP_STIR);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Stop stirring, monitoring temperature\n");
        }
    }
}

/**
 * @brief �ȴ�ֹͣ����״̬ - ����¶�����1-2��C��ֹͣ����
 */
static void brewing_state_wait_stop_stir_handler(void)
{
    float current_temp;
    
    // ��鳬ʱ
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }
    
    // ��ȡ��ǰ�¶�
    if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
        current_temp = g_brewing_temp_sensor.current_temp;
        float temp_rise = current_temp - g_brewing_ctrl.initial_temperature;
        
        // ����¶��Ƿ�����1-2��C
        if(temp_rise >= BREWING_TEMP_THRESHOLD_MIN && temp_rise <= BREWING_TEMP_THRESHOLD_MAX) {
            Heater_Stop();  // ֹͣ����
            brewing_change_state(BREWING_STATE_RESTING);
            
            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing: Temperature rise %.1f��C, stop heating\n", temp_rise);
            }
        }
    } else {
        g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
        brewing_change_state(BREWING_STATE_ERROR);
    }
}

/**
 * @brief ����״̬���� - �ȴ�30���ʼ����
 */
static void brewing_state_resting_handler(void)
{
    uint32_t rest_time = (uwtick - g_brewing_ctrl.state_start_time);
    
    if(rest_time >= (BREWING_REST_TIME_SEC * 1000)) {  // 30�뾲�����
        // ����ˮ��2��ʼ����
        WaterPump_Control(PUMP_2, PUMP_ON_STATE);
        brewing_change_state(BREWING_STATE_FILTERING);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start filtering\n");
        }
    }
}

/**
 * @brief ����״̬���� - ����ʱ����ڼ�ˮʱ��
 */
static void brewing_state_filtering_handler(void)
{
    uint32_t filter_time = (uwtick - g_brewing_ctrl.state_start_time);
    
    if(filter_time >= g_brewing_ctrl.water_fill_time) {  // ����ʱ����ڼ�ˮʱ��
        WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // ֹͣˮ��2
        brewing_change_state(BREWING_STATE_COMPLETE);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Filtering complete, total time: %dms\n", filter_time);
        }
    }
}

/**
 * @brief ���״̬���� - �������
 */
static void brewing_state_complete_handler(void)
{
    g_brewing_ctrl.process_active = 0;  // ͣ������
    
    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Process completed successfully\n");
    }
}

/**
 * @brief ����״̬���� - ��ȫֹͣ�����豸
 */
static void brewing_state_error_handler(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.process_active = 0;
    
    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Error occurred, code: %d\n", g_brewing_ctrl.error_code);
    }
}

/**
 * @brief ״̬�л�����
 */
static void brewing_change_state(BrewingState_t new_state)
{
    g_brewing_ctrl.current_state = new_state;
    g_brewing_ctrl.state_start_time = uwtick;
    
    if(g_brewing_ctrl.debug_mode) {
        brewing_debug_print_state();
    }
}

/**
 * @brief ��ʱ���
 */
static uint8_t brewing_check_timeout(uint32_t timeout_ms)
{
    return ((uwtick - g_brewing_ctrl.state_start_time) >= timeout_ms);
}

/**
 * @brief ����״̬���
 */
static void brewing_debug_print_state(void)
{
    const char* state_names[] = {
        "IDLE", "FILLING", "WAIT_HEAT", "HEATING", "WAIT_STIR",
        "STIRRING", "WAIT_STOP_STIR", "RESTING", "FILTERING", "COMPLETE", "ERROR"
    };
    
    if(g_brewing_ctrl.current_state <= BREWING_STATE_ERROR) {
        printf("Brewing State: %s\n", state_names[g_brewing_ctrl.current_state]);
    }
}

/**
 * @brief ����ֹͣ�����豸
 */
static void brewing_emergency_stop_all(void)
{
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // ֹͣˮ��1
    WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // ֹͣˮ��2
    Heater_Stop();                              // ֹͣ����
    stir_360(0, 0);                            // ֹͣ����
}

// === �����ӿں���ʵ�� ===

/**
 * @brief ��ȡ��ǰ״̬
 */
BrewingState_t brewing_get_current_state(void)
{
    return g_brewing_ctrl.current_state;
}

/**
 * @brief ��ȡ�������
 */
BrewingError_t brewing_get_error_code(void)
{
    return g_brewing_ctrl.error_code;
}

/**
 * @brief �������̵���ʼ״̬
 */
void brewing_reset_process(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.state_start_time = uwtick;
    
    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Process reset\n");
    }
}

/**
 * @brief ����ֹͣ�����豸
 */
void brewing_emergency_stop(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
    brewing_change_state(BREWING_STATE_ERROR);
}

/**
 * @brief ���õ���ģʽ
 */
void brewing_set_debug_mode(uint8_t enable)
{
    g_brewing_ctrl.debug_mode = enable;
}
