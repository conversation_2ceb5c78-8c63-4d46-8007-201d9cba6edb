/**
 * @file    brewing_control.c
 * @brief   ????????????????????????? - 8??????????????
 * <AUTHOR> @date    2025-01-26
 */

#include "brewing_control.h"
#include "ds18b20.h"  // 包含DS18B20头文件以使用完整类型定义
#include "key.h"      // 按键控制
#include "water_pump.h" // 水泵控制
#include "heater.h"   // 加热器控制
#include "water_level.h" // 水位检测
#include "timer.h"    // 定时器
#include "lcd.h"      // 液晶显示

// === ?????????? ===
extern unsigned long int uwtick;  // ????????(??????main.c)

// === ?????????? ===
BrewingControl_t g_brewing_ctrl = {0};              // ??????????
DS18B20_HandleTypeDef g_brewing_temp_sensor = {0};  // ???????????

// === ??????????? ===
static void brewing_state_idle_handler(void);
static void brewing_state_filling_handler(void);
static void brewing_state_wait_heat_handler(void);
static void brewing_state_heating_handler(void);
static void brewing_state_wait_stir_handler(void);  // ?????????????
static void brewing_state_stirring_handler(void);
static void brewing_state_wait_stop_stir_handler(void);
static void brewing_state_resting_handler(void);
static void brewing_state_filtering_handler(void);
static void brewing_state_complete_handler(void);
static void brewing_state_error_handler(void);
static void brewing_change_state(BrewingState_t new_state);
static uint8_t brewing_check_timeout(uint32_t timeout_ms);
static void brewing_debug_print_state(void);
static void brewing_emergency_stop_all(void);
static void brewing_system_health_check(void);
static void brewing_log_state_transition(BrewingState_t old_state, BrewingState_t new_state);

/**
 * @brief ????????????????
 */
void brewing_control_init(void)
{
    // ????????????
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.state_start_time = uwtick;
    g_brewing_ctrl.water_fill_time = 0;
    g_brewing_ctrl.initial_temperature = 0.0f;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.debug_mode = 0;

    // ?????????????
    DS18B20_Init(&g_brewing_temp_sensor, 100.0f);  // ??????????

    // ??????????????????
    brewing_emergency_stop_all();

    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing Control: Initialized\n");
    }
}

/**
 * @brief ?????????????(?????????????)
 */
void brewing_control_proc(void)
{
    // ????????????
    if(g_brewing_ctrl.current_state != BREWING_STATE_IDLE &&
       g_brewing_ctrl.current_state != BREWING_STATE_ERROR) {
        brewing_system_health_check();
    }

    // ????????????
    if(!g_brewing_ctrl.process_active && g_brewing_ctrl.current_state != BREWING_STATE_IDLE) {
        return;
    }

    // ????????
    switch(g_brewing_ctrl.current_state) {
        case BREWING_STATE_IDLE:
            brewing_state_idle_handler();
            break;
        case BREWING_STATE_FILLING:
            brewing_state_filling_handler();
            break;
        case BREWING_STATE_WAIT_HEAT:
            brewing_state_wait_heat_handler();
            break;
        case BREWING_STATE_HEATING:
            brewing_state_heating_handler();
            break;
        case BREWING_STATE_WAIT_STIR:
            brewing_state_wait_stir_handler();
            break;
        case BREWING_STATE_STIRRING:
            brewing_state_stirring_handler();
            break;
        case BREWING_STATE_WAIT_STOP_STIR:
            brewing_state_wait_stop_stir_handler();
            break;
        case BREWING_STATE_RESTING:
            brewing_state_resting_handler();
            break;
        case BREWING_STATE_FILTERING:
            brewing_state_filtering_handler();
            break;
        case BREWING_STATE_COMPLETE:
            brewing_state_complete_handler();
            break;
        case BREWING_STATE_ERROR:
            brewing_state_error_handler();
            break;
        default:
            g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
            brewing_change_state(BREWING_STATE_ERROR);
            break;
    }
}

/**
 * @brief ?????????? - ???????1???????????
 */
static void brewing_state_idle_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_WATER_CONFIRM) {  // ????1 - ?????
        g_brewing_ctrl.process_active = 1;
        g_brewing_ctrl.water_fill_time = 0;

        // ???????1??????
        WaterPump_Control(PUMP_1, PUMP_ON_STATE);

        brewing_change_state(BREWING_STATE_FILLING);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start filling water\n");
        }
    }
}

/**
 * @brief ????????? - ?????????????????
 */
static void brewing_state_filling_handler(void)
{
    // ??????
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // ?????????
    g_brewing_ctrl.water_fill_time += BREWING_TASK_PERIOD_MS;

    // ??????
    uint8_t water_level_status = WaterLevel_Detect();
    if(water_level_status == 1) {  // ????????
        WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // ??????1
        brewing_change_state(BREWING_STATE_WAIT_HEAT);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Water level reached, fill time: %dms\n", g_brewing_ctrl.water_fill_time);
        }
    } else {
        // ???????????????????????
        if(g_brewing_ctrl.water_fill_time > (BREWING_TIMEOUT_SEC * 500)) {  // ???????????????????????
            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing: Water level sensor may be faulty\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_WATER_LEVEL;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }
}

/**
 * @brief ????????????? - ???????2????????
 */
static void brewing_state_wait_heat_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_HEAT_CONFIRM) {  // ????2 - ??????
        // ?????????
        if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
            g_brewing_ctrl.initial_temperature = g_brewing_temp_sensor.current_temp;

            // ??????????
            Heater_Start();
            brewing_change_state(BREWING_STATE_HEATING);
        } else {
            // ?????????????????
            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing: Temperature sensor error during heating start\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
            brewing_change_state(BREWING_STATE_ERROR);
            return;
        }

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start heating, initial temp: %.1f??C\n", g_brewing_ctrl.initial_temperature);
        }
    }
}

/**
 * @brief ?????????? - ???????3????????
 */
static void brewing_state_heating_handler(void)
{
    uint8_t key = key_read();

    // ??????
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    if(key == KEY_STIR_START) {  // ????3 - ????????
        // ??????????(??????????)
        stir_360(STIR_DIRECTION_FORWARD, STIR_SPEED_MAX);
        brewing_change_state(BREWING_STATE_STIRRING);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start stirring\n");
        }
    }
}

/**
 * @brief ????????????? - ???????3???????
 */
static void brewing_state_wait_stir_handler(void)
{
    uint8_t key = key_read();

    // ??????
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    if(key == KEY_STIR_START) {  // ????3 - ???????
        // ??????????(???????????)
        stir_360(STIR_DIRECTION_FORWARD, STIR_SPEED_MAX);
        brewing_change_state(BREWING_STATE_STIRRING);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start stirring\n");
        }
    }
}

/**
 * @brief ?????????? - ???????4??????
 */
static void brewing_state_stirring_handler(void)
{
    uint8_t key = key_read();

    if(key == KEY_STIR_STOP) {  // ????4 - ??????
        stir_360(0, 0);  // ??????
        brewing_change_state(BREWING_STATE_WAIT_STOP_STIR);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Stop stirring, monitoring temperature\n");
        }
    }
}

/**
 * @brief ??????????? - ??????????1-2??C???????
 */
static void brewing_state_wait_stop_stir_handler(void)
{
    float current_temp;

    // ??????
    if(brewing_check_timeout(BREWING_TIMEOUT_SEC * 1000)) {
        g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // ?????????
    if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) == DS18B20_OK) {
        current_temp = g_brewing_temp_sensor.current_temp;
        float temp_rise = current_temp - g_brewing_ctrl.initial_temperature;

        // ?????????????1-2??C
        if(temp_rise >= BREWING_TEMP_THRESHOLD_MIN && temp_rise <= BREWING_TEMP_THRESHOLD_MAX) {
            Heater_Stop();  // ??????
            brewing_change_state(BREWING_STATE_RESTING);

            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing: Temperature rise %.1f??C, stop heating\n", temp_rise);
            }
        }
    } else {
        g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
        brewing_change_state(BREWING_STATE_ERROR);
    }
}

/**
 * @brief ?????????? - ???30???????????
 */
static void brewing_state_resting_handler(void)
{
    uint32_t rest_time = (uwtick - g_brewing_ctrl.state_start_time);

    if(rest_time >= (BREWING_REST_TIME_SEC * 1000)) {  // 30???????
        // ???????2???????
        WaterPump_Control(PUMP_2, PUMP_ON_STATE);
        brewing_change_state(BREWING_STATE_FILTERING);

        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Start filtering\n");
        }
    }
}

/**
 * @brief ?????????? - ???????????????
 */
static void brewing_state_filtering_handler(void)
{
    uint32_t filter_time = (uwtick - g_brewing_ctrl.state_start_time);

    if(filter_time >= g_brewing_ctrl.water_fill_time) {  // ???????????????
        WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // ??????2
        brewing_change_state(BREWING_STATE_COMPLETE);
        
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing: Filtering complete, total time: %dms\n", filter_time);
        }
    }
}

/**
 * @brief ????????? - ???????????
 */
static void brewing_state_complete_handler(void)
{
    g_brewing_ctrl.process_active = 0;  // ?????????

    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Process completed successfully\n");
    }
}

/**
 * @brief ?????????? - ?????????????
 */
static void brewing_state_error_handler(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.process_active = 0;

    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Error occurred, code: %d\n", g_brewing_ctrl.error_code);
    }
}

/**
 * @brief ??????????
 */
static void brewing_change_state(BrewingState_t new_state)
{
    BrewingState_t old_state = g_brewing_ctrl.current_state;

    // ???????????
    brewing_log_state_transition(old_state, new_state);

    g_brewing_ctrl.current_state = new_state;
    g_brewing_ctrl.state_start_time = uwtick;

    if(g_brewing_ctrl.debug_mode) {
        brewing_debug_print_state();
    }
}

/**
 * @brief ??????
 */
static uint8_t brewing_check_timeout(uint32_t timeout_ms)
{
    return ((uwtick - g_brewing_ctrl.state_start_time) >= timeout_ms);
}

/**
 * @brief ?????????
 */
static void brewing_debug_print_state(void)
{
    const char* state_names[] = {
        "IDLE", "FILLING", "WAIT_HEAT", "HEATING", "WAIT_STIR",
        "STIRRING", "WAIT_STOP_STIR", "RESTING", "FILTERING", "COMPLETE", "ERROR"
    };

    if(g_brewing_ctrl.current_state <= BREWING_STATE_ERROR) {
        printf("Brewing State: %s\n", state_names[g_brewing_ctrl.current_state]);
    }
}

/**
 * @brief ?????????????
 */
static void brewing_emergency_stop_all(void)
{
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);  // ??????1
    WaterPump_Control(PUMP_2, PUMP_OFF_STATE);  // ??????2
    Heater_Stop();                              // ??????
    stir_360(0, 0);                            // ??????
}

// === ????????????? ===

/**
 * @brief ????????
 */
BrewingState_t brewing_get_current_state(void)
{
    return g_brewing_ctrl.current_state;
}

/**
 * @brief ??????????
 */
BrewingError_t brewing_get_error_code(void)
{
    return g_brewing_ctrl.error_code;
}

/**
 * @brief ??????????????
 */
void brewing_reset_process(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.current_state = BREWING_STATE_IDLE;
    g_brewing_ctrl.error_code = BREWING_ERROR_NONE;
    g_brewing_ctrl.process_active = 0;
    g_brewing_ctrl.state_start_time = uwtick;

    if(g_brewing_ctrl.debug_mode) {
        printf("Brewing: Process reset\n");
    }
}

/**
 * @brief ?????????????
 */
void brewing_emergency_stop(void)
{
    brewing_emergency_stop_all();
    g_brewing_ctrl.error_code = BREWING_ERROR_HARDWARE;
    brewing_change_state(BREWING_STATE_ERROR);
}

/**
 * @brief ?????????
 */
void brewing_set_debug_mode(uint8_t enable)
{
    g_brewing_ctrl.debug_mode = enable;
}

/**
 * @brief ?????????D??
 */
static void brewing_system_health_check(void)
{
    // ?????????????
    if(DS18B20_ReadRealtimeTemp(&g_brewing_temp_sensor) != DS18B20_OK) {
        if(g_brewing_ctrl.debug_mode) {
            printf("Brewing Health: Temperature sensor error\n");
        }
        g_brewing_ctrl.error_code = BREWING_ERROR_TEMPERATURE;
        brewing_change_state(BREWING_STATE_ERROR);
        return;
    }

    // ??????????????
    uint8_t water_level = WaterLevel_Detect();
    if(g_brewing_ctrl.current_state == BREWING_STATE_FILLING && water_level == 0) {
        // ??????????????????????????????????????
        if(g_brewing_ctrl.water_fill_time > (BREWING_TIMEOUT_SEC * 800)) {
            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing Health: Water level sensor timeout\n");
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_WATER_LEVEL;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }

    // ???????????
    if(g_brewing_ctrl.current_state != BREWING_STATE_IDLE &&
       g_brewing_ctrl.current_state != BREWING_STATE_COMPLETE &&
       g_brewing_ctrl.current_state != BREWING_STATE_ERROR) {
        uint32_t state_duration = uwtick - g_brewing_ctrl.state_start_time;
        if(state_duration > (BREWING_TIMEOUT_SEC * 1200)) {  // ????????????
            if(g_brewing_ctrl.debug_mode) {
                printf("Brewing Health: State timeout, duration: %dms\n", state_duration);
            }
            g_brewing_ctrl.error_code = BREWING_ERROR_TIMEOUT;
            brewing_change_state(BREWING_STATE_ERROR);
        }
    }
}

/**
 * @brief ???????????
 */
static void brewing_log_state_transition(BrewingState_t old_state, BrewingState_t new_state)
{
    const char* state_names[] = {
        "IDLE", "FILLING", "WAIT_HEAT", "HEATING", "WAIT_STIR",
        "STIRRING", "WAIT_STOP_STIR", "RESTING", "FILTERING", "COMPLETE", "ERROR"
    };

    if(g_brewing_ctrl.debug_mode && old_state != new_state) {
        if(old_state <= BREWING_STATE_ERROR && new_state <= BREWING_STATE_ERROR) {
            printf("Brewing Transition: %s -> %s (time: %dms)\n",
                   state_names[old_state], state_names[new_state], uwtick);
        }
    }
}

/**
 * @brief ???????????????
 */
uint32_t brewing_get_state_duration(void)
{
    return (uwtick - g_brewing_ctrl.state_start_time);
}

/**
 * @brief ?????????????????
 */
uint8_t brewing_is_process_active(void)
{
    return g_brewing_ctrl.process_active;
}

/**
 * @brief ????????????
 */
void brewing_perform_health_check(void)
{
    brewing_system_health_check();
}
